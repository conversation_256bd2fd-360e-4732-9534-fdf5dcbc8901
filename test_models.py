"""
Comprehensive test script for text classification models.
"""

import pandas as pd
from text_classifier import TextClassifier, create_sample_data


def test_model_comparison():
    """Test and compare different model combinations."""
    print("=== Model Comparison Test ===\n")
    
    # Create sample data
    texts, labels = create_sample_data()
    print(f"Dataset: {len(texts)} samples, {len(set(labels))} classes")
    
    # Initialize classifier
    classifier = TextClassifier()
    
    # Compare all model combinations
    print("\nComparing all model and vectorizer combinations...")
    comparison_df = classifier.compare_models(texts, labels, test_size=0.3)
    
    print("\nModel Comparison Results:")
    print("=" * 60)
    print(comparison_df.round(4).to_string(index=False))
    
    # Find best model
    best_idx = comparison_df['Accuracy'].idxmax()
    best_model = comparison_df.iloc[best_idx]
    
    print(f"\nBest Model: {best_model['Model']} with {best_model['Vectorizer']}")
    print(f"Best Accuracy: {best_model['Accuracy']:.4f}")
    
    return comparison_df


def test_hyperparameter_tuning():
    """Test hyperparameter tuning."""
    print("\n=== Hyperparameter Tuning Test ===\n")
    
    # Create larger dataset for better tuning
    texts, labels = create_sample_data()
    # Duplicate data to have more samples
    texts = texts * 3
    labels = labels * 3
    
    classifier = TextClassifier()
    
    print("Training with hyperparameter tuning...")
    results = classifier.train_model(
        texts=texts,
        labels=labels,
        model_name='logistic_regression',
        vectorizer_name='tfidf',
        test_size=0.2,
        hyperparameter_tuning=True
    )
    
    print(f"Tuned model performance:")
    for metric, value in results['metrics'].items():
        print(f"  {metric.capitalize()}: {value:.4f}")


def test_model_persistence():
    """Test saving and loading models."""
    print("\n=== Model Persistence Test ===\n")
    
    texts, labels = create_sample_data()
    
    # Train a model
    classifier = TextClassifier()
    results = classifier.train_model(texts, labels, test_size=0.3)
    
    # Save the model
    model_file = 'test_model.pkl'
    classifier.save_model('logistic_regression_tfidf', model_file)
    print(f"Model saved to {model_file}")
    
    # Create new classifier and load the model
    new_classifier = TextClassifier()
    new_classifier.load_model(model_file, 'logistic_regression_tfidf')
    print(f"Model loaded from {model_file}")
    
    # Test predictions with loaded model
    test_texts = ["Great product!", "Terrible experience."]
    predictions = new_classifier.predict(test_texts, 'logistic_regression_tfidf')
    
    print("Predictions with loaded model:")
    for text, pred in zip(test_texts, predictions):
        print(f"  '{text}' -> {pred}")


def test_custom_preprocessing():
    """Test custom preprocessing options."""
    print("\n=== Custom Preprocessing Test ===\n")
    
    texts, labels = create_sample_data()
    classifier = TextClassifier()
    
    # Test different preprocessing configurations
    configs = [
        {"preprocess": False, "name": "No preprocessing"},
        {"preprocess": True, "name": "Standard preprocessing"},
    ]
    
    for config in configs:
        print(f"\nTesting: {config['name']}")
        results = classifier.train_model(
            texts, labels, 
            test_size=0.3,
            preprocess=config['preprocess']
        )
        print(f"  Accuracy: {results['metrics']['accuracy']:.4f}")


def test_evaluation():
    """Test model evaluation features."""
    print("\n=== Model Evaluation Test ===\n")
    
    texts, labels = create_sample_data()
    classifier = TextClassifier()
    
    # Train model
    classifier.train_model(texts, labels, test_size=0.3)
    
    # Evaluate on the same data (for demonstration)
    evaluation = classifier.evaluate_model(texts, labels, 'logistic_regression_tfidf')
    
    print("Detailed Evaluation Results:")
    print(f"Overall Accuracy: {evaluation['metrics']['accuracy']:.4f}")
    
    print("\nPer-class Performance:")
    report = evaluation['classification_report']
    for class_name in ['positive', 'negative']:
        if class_name in report:
            metrics = report[class_name]
            print(f"  {class_name.capitalize()}:")
            print(f"    Precision: {metrics['precision']:.4f}")
            print(f"    Recall: {metrics['recall']:.4f}")
            print(f"    F1-score: {metrics['f1-score']:.4f}")


def test_prediction_probabilities():
    """Test prediction probabilities."""
    print("\n=== Prediction Probabilities Test ===\n")
    
    texts, labels = create_sample_data()
    classifier = TextClassifier()
    
    # Train model
    classifier.train_model(texts, labels, test_size=0.3)
    
    # Test texts with varying sentiment strength
    test_texts = [
        "Absolutely amazing product! Love it!",
        "It's okay, nothing special.",
        "Completely terrible and awful!"
    ]
    
    predictions = classifier.predict(test_texts, 'logistic_regression_tfidf')
    probabilities = classifier.predict_proba(test_texts, 'logistic_regression_tfidf')
    
    print("Predictions with confidence scores:")
    for i, (text, pred, prob) in enumerate(zip(test_texts, predictions, probabilities)):
        confidence = max(prob)
        print(f"  Text {i+1}: '{text}'")
        print(f"    Prediction: {pred}")
        print(f"    Confidence: {confidence:.4f}")
        print(f"    Probabilities: positive={prob[1]:.4f}, negative={prob[0]:.4f}")
        print()


def main():
    """Run all tests."""
    print("🚀 Starting Comprehensive Text Classification Tests\n")
    
    try:
        # Run all tests
        comparison_df = test_model_comparison()
        test_custom_preprocessing()
        test_evaluation()
        test_prediction_probabilities()
        test_model_persistence()
        
        # Only run hyperparameter tuning if user wants (it takes longer)
        print("\n" + "="*60)
        print("Note: Hyperparameter tuning test skipped (takes longer)")
        print("Uncomment the line below to run it:")
        print("# test_hyperparameter_tuning()")
        
        print("\n✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

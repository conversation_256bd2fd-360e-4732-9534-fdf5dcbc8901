"""
Simple text classification demo without plotting.
"""

from text_classifier import TextClassifier, create_sample_data


def main():
    print("=== Simple Text Classification Demo ===\n")
    
    # Create sample data
    print("1. Loading sample data...")
    texts, labels = create_sample_data()
    print(f"Loaded {len(texts)} samples with {len(set(labels))} classes: {set(labels)}")
    
    # Initialize classifier
    print("\n2. Initializing text classifier...")
    classifier = TextClassifier()
    
    # Train Logistic Regression with TF-IDF
    print("\n3. Training Logistic Regression with TF-IDF...")
    lr_results = classifier.train_model(
        texts=texts,
        labels=labels,
        model_name='logistic_regression',
        vectorizer_name='tfidf',
        test_size=0.3,
        preprocess=True
    )
    
    print(f"Logistic Regression Results:")
    for metric, value in lr_results['metrics'].items():
        print(f"  {metric.capitalize()}: {value:.4f}")
    
    # Train Naive Bayes with Count Vectorizer
    print("\n4. Training Naive <PERSON>es with Count Vectorizer...")
    nb_results = classifier.train_model(
        texts=texts,
        labels=labels,
        model_name='naive_bayes',
        vectorizer_name='count',
        test_size=0.3,
        preprocess=True
    )
    
    print(f"Naive Bayes Results:")
    for metric, value in nb_results['metrics'].items():
        print(f"  {metric.capitalize()}: {value:.4f}")
    
    # Test predictions on new data
    print("\n5. Testing predictions on new data...")
    test_texts = [
        "This product is absolutely fantastic! I love it!",
        "Terrible quality and poor service. Very disappointed.",
        "Good value for money and works as expected.",
        "Worst experience ever. Would not recommend to anyone."
    ]
    
    # Use the logistic regression model
    best_pipeline = 'logistic_regression_tfidf'
    
    predictions = classifier.predict(test_texts, best_pipeline)
    probabilities = classifier.predict_proba(test_texts, best_pipeline)
    
    print(f"Predictions using {best_pipeline}:")
    for i, (text, pred, prob) in enumerate(zip(test_texts, predictions, probabilities)):
        print(f"  Text {i+1}: '{text[:50]}...'")
        print(f"  Prediction: {pred}")
        print(f"  Confidence: {max(prob):.4f}")
        print()
    
    # Save the model
    print("6. Saving the model...")
    classifier.save_model(best_pipeline, 'text_classifier_model.pkl')
    
    print("\n=== Demo Complete! ===")
    print(f"Model saved as 'text_classifier_model.pkl'")


if __name__ == "__main__":
    main()

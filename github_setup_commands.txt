GitHub Repository Setup Commands
=====================================

After creating the repository on GitHub (https://github.com/new), run these commands:

1. Add remote repository:
git remote add origin https://github.com/shamanthshettykr/Fake-news-detection-system.git

2. Rename branch to main:
git branch -M main

3. Push to GitHub:
git push -u origin main

Alternative using GitHub CLI (if installed):
gh repo create Fake-news-detection-system --public --description "A machine learning system for detecting fake news using Logistic Regression and Naive Bayes algorithms" --push

Repository Details:
- Name: Fake-news-detection-system
- Description: A machine learning system for detecting fake news using Logistic Regression and Naive Bayes algorithms
- Visibility: Public
- Initialize: No (we already have files)

Your repository will be available at:
https://github.com/shamanthshettykr/Fake-news-detection-system

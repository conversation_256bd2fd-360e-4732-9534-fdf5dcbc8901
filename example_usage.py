"""
Example usage of the TextPreprocessor module.
"""

from text_preprocessor import TextPreprocessor, preprocess_text, preprocess_texts


def main():
    # Sample texts for demonstration
    sample_texts = [
        "Hello World! This is a SAMPLE text with punctuation, numbers123, and extra    spaces.",
        "Visit https://example.com <NAME_EMAIL> for more information.",
        "This text contains stopwords like 'the', 'and', 'is' that we might want to remove.",
        "UPPERCASE text, lowercase text, and MiXeD cAsE text should all be normalized.",
        "Special characters like @#$%^&*() can be removed if needed!!!"
    ]
    
    print("=== Text Preprocessing Examples ===\n")
    
    # Example 1: Basic preprocessing
    print("1. Basic preprocessing (lowercase, remove punctuation, remove stopwords):")
    print("-" * 60)
    
    for i, text in enumerate(sample_texts, 1):
        cleaned = preprocess_text(text)
        print(f"Original {i}: {text}")
        print(f"Cleaned {i}:  {cleaned}")
        print()
    
    # Example 2: Custom preprocessing options
    print("\n2. Custom preprocessing (keep numbers, remove special characters):")
    print("-" * 60)
    
    text = "Contact <NAME_EMAIL> or call 555-123-4567! Visit www.example.com."
    
    # Different preprocessing configurations
    configs = [
        {"remove_nums": False, "remove_special": True, "remove_emails": False},
        {"remove_nums": True, "remove_punct": False, "remove_stops": False},
        {"lowercase": False, "remove_punct": False, "remove_stops": True}
    ]
    
    print(f"Original: {text}")
    
    for i, config in enumerate(configs, 1):
        cleaned = preprocess_text(text, **config)
        print(f"Config {i}: {cleaned}")
        print(f"Settings: {config}")
        print()
    
    # Example 3: Using the TextPreprocessor class directly
    print("\n3. Using TextPreprocessor class with custom stopwords:")
    print("-" * 60)
    
    # Custom stopwords
    custom_stopwords = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for'}
    preprocessor = TextPreprocessor(custom_stopwords=custom_stopwords)
    
    text = "The quick brown fox jumps over the lazy dog in the park."
    cleaned = preprocessor.clean_text(text)
    
    print(f"Original: {text}")
    print(f"Cleaned:  {cleaned}")
    print(f"Custom stopwords: {custom_stopwords}")
    
    # Example 4: Batch processing
    print("\n4. Batch processing multiple texts:")
    print("-" * 60)
    
    batch_texts = [
        "First document with some TEXT!",
        "Second document with different CONTENT.",
        "Third document with more INFORMATION."
    ]
    
    cleaned_batch = preprocess_texts(batch_texts)
    
    for original, cleaned in zip(batch_texts, cleaned_batch):
        print(f"Original: {original}")
        print(f"Cleaned:  {cleaned}")
        print()
    
    # Example 5: Step-by-step preprocessing
    print("\n5. Step-by-step preprocessing demonstration:")
    print("-" * 60)
    
    text = "Hello! This is a SAMPLE text with 123 numbers and extra    spaces."
    preprocessor = TextPreprocessor()
    
    print(f"Original:              {text}")
    print(f"Lowercase:             {preprocessor.to_lowercase(text)}")
    print(f"Remove punctuation:    {preprocessor.remove_punctuation(preprocessor.to_lowercase(text))}")
    print(f"Remove numbers:        {preprocessor.remove_numbers(preprocessor.remove_punctuation(preprocessor.to_lowercase(text)))}")
    print(f"Remove extra spaces:   {preprocessor.remove_extra_whitespace(preprocessor.remove_numbers(preprocessor.remove_punctuation(preprocessor.to_lowercase(text))))}")
    
    final_text = preprocessor.clean_text(text, remove_nums=True)
    print(f"Final result:          {final_text}")


if __name__ == "__main__":
    main()

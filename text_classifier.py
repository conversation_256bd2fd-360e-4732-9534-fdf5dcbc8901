"""
Text Classification Module

This module provides text classification using Logistic Regression and Naive Bayes.
Includes data preprocessing, feature extraction, model training, and evaluation.
"""

import pandas as pd
import numpy as np
import pickle
import json
from typing import List, Tuple, Dict, Any, Optional
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.linear_model import LogisticRegression
from sklearn.naive_bayes import MultinomialNB
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    classification_report, confusion_matrix
)
from sklearn.pipeline import Pipeline
import matplotlib.pyplot as plt
import seaborn as sns

from text_preprocessor import TextPreprocessor


class TextClassifier:
    """A comprehensive text classification class supporting multiple algorithms."""
    
    def __init__(self, preprocessor: Optional[TextPreprocessor] = None):
        """
        Initialize the TextClassifier.
        
        Args:
            preprocessor: Optional TextPreprocessor instance
        """
        self.preprocessor = preprocessor or TextPreprocessor()
        self.models = {}
        self.vectorizers = {}
        self.pipelines = {}
        self.is_trained = {}
        
        # Available models
        self.available_models = {
            'logistic_regression': LogisticRegression(random_state=42, max_iter=1000),
            'naive_bayes': MultinomialNB()
        }
        
        # Available vectorizers
        self.available_vectorizers = {
            'tfidf': TfidfVectorizer(max_features=10000, ngram_range=(1, 2)),
            'count': CountVectorizer(max_features=10000, ngram_range=(1, 2))
        }
    
    def load_data(self, data_path: str = None, texts: List[str] = None, 
                  labels: List[str] = None) -> Tuple[List[str], List[str]]:
        """
        Load data from file or from provided lists.
        
        Args:
            data_path: Path to CSV file with 'text' and 'label' columns
            texts: List of text samples
            labels: List of corresponding labels
            
        Returns:
            Tuple of (texts, labels)
        """
        if data_path:
            df = pd.read_csv(data_path)
            if 'text' not in df.columns or 'label' not in df.columns:
                raise ValueError("CSV file must contain 'text' and 'label' columns")
            return df['text'].tolist(), df['label'].tolist()
        
        elif texts and labels:
            if len(texts) != len(labels):
                raise ValueError("Texts and labels must have the same length")
            return texts, labels
        
        else:
            raise ValueError("Either provide data_path or both texts and labels")
    
    def preprocess_texts(self, texts: List[str]) -> List[str]:
        """Preprocess a list of texts."""
        return self.preprocessor.preprocess_batch(texts)
    
    def train_model(self, texts: List[str], labels: List[str], 
                   model_name: str = 'logistic_regression',
                   vectorizer_name: str = 'tfidf',
                   test_size: float = 0.2,
                   preprocess: bool = True,
                   hyperparameter_tuning: bool = False) -> Dict[str, Any]:
        """
        Train a text classification model.
        
        Args:
            texts: List of text samples
            labels: List of corresponding labels
            model_name: Name of the model ('logistic_regression' or 'naive_bayes')
            vectorizer_name: Name of the vectorizer ('tfidf' or 'count')
            test_size: Proportion of data to use for testing
            preprocess: Whether to preprocess texts
            hyperparameter_tuning: Whether to perform hyperparameter tuning
            
        Returns:
            Dictionary with training results and metrics
        """
        if model_name not in self.available_models:
            raise ValueError(f"Model {model_name} not available. Choose from: {list(self.available_models.keys())}")
        
        if vectorizer_name not in self.available_vectorizers:
            raise ValueError(f"Vectorizer {vectorizer_name} not available. Choose from: {list(self.available_vectorizers.keys())}")
        
        # Preprocess texts if requested
        if preprocess:
            print("Preprocessing texts...")
            processed_texts = self.preprocess_texts(texts)
        else:
            processed_texts = texts
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            processed_texts, labels, test_size=test_size, random_state=42, stratify=labels
        )
        
        # Create pipeline
        vectorizer = self.available_vectorizers[vectorizer_name]
        model = self.available_models[model_name]
        
        pipeline = Pipeline([
            ('vectorizer', vectorizer),
            ('classifier', model)
        ])
        
        # Hyperparameter tuning
        if hyperparameter_tuning:
            print("Performing hyperparameter tuning...")
            pipeline = self._tune_hyperparameters(pipeline, X_train, y_train, model_name, vectorizer_name)
        
        # Train the model
        print(f"Training {model_name} with {vectorizer_name} vectorizer...")
        pipeline.fit(X_train, y_train)
        
        # Make predictions
        y_pred = pipeline.predict(X_test)
        
        # Calculate metrics
        metrics = self._calculate_metrics(y_test, y_pred)
        
        # Store the trained pipeline
        pipeline_key = f"{model_name}_{vectorizer_name}"
        self.pipelines[pipeline_key] = pipeline
        self.is_trained[pipeline_key] = True
        
        # Prepare results
        results = {
            'model_name': model_name,
            'vectorizer_name': vectorizer_name,
            'pipeline_key': pipeline_key,
            'metrics': metrics,
            'test_size': test_size,
            'train_samples': len(X_train),
            'test_samples': len(X_test),
            'classes': list(set(labels))
        }
        
        print(f"Training completed! Accuracy: {metrics['accuracy']:.4f}")
        return results
    
    def _tune_hyperparameters(self, pipeline, X_train, y_train, model_name, vectorizer_name):
        """Perform hyperparameter tuning using GridSearchCV."""
        param_grids = {
            'logistic_regression': {
                'classifier__C': [0.1, 1, 10, 100],
                'classifier__penalty': ['l1', 'l2'],
                'classifier__solver': ['liblinear']
            },
            'naive_bayes': {
                'classifier__alpha': [0.1, 0.5, 1.0, 2.0]
            }
        }
        
        if vectorizer_name == 'tfidf':
            param_grids[model_name].update({
                'vectorizer__max_features': [5000, 10000, 15000],
                'vectorizer__ngram_range': [(1, 1), (1, 2)]
            })
        
        param_grid = param_grids.get(model_name, {})
        
        grid_search = GridSearchCV(
            pipeline, param_grid, cv=5, scoring='accuracy', n_jobs=-1, verbose=1
        )
        grid_search.fit(X_train, y_train)
        
        print(f"Best parameters: {grid_search.best_params_}")
        print(f"Best cross-validation score: {grid_search.best_score_:.4f}")
        
        return grid_search.best_estimator_
    
    def _calculate_metrics(self, y_true, y_pred) -> Dict[str, float]:
        """Calculate classification metrics."""
        return {
            'accuracy': accuracy_score(y_true, y_pred),
            'precision': precision_score(y_true, y_pred, average='weighted'),
            'recall': recall_score(y_true, y_pred, average='weighted'),
            'f1_score': f1_score(y_true, y_pred, average='weighted')
        }
    
    def predict(self, texts: List[str], pipeline_key: str, 
                preprocess: bool = True) -> List[str]:
        """
        Make predictions on new texts.
        
        Args:
            texts: List of texts to classify
            pipeline_key: Key of the trained pipeline to use
            preprocess: Whether to preprocess texts
            
        Returns:
            List of predicted labels
        """
        if pipeline_key not in self.pipelines:
            raise ValueError(f"Pipeline {pipeline_key} not found. Train a model first.")
        
        if preprocess:
            processed_texts = self.preprocess_texts(texts)
        else:
            processed_texts = texts
        
        return self.pipelines[pipeline_key].predict(processed_texts)
    
    def predict_proba(self, texts: List[str], pipeline_key: str, 
                     preprocess: bool = True) -> np.ndarray:
        """
        Get prediction probabilities for new texts.
        
        Args:
            texts: List of texts to classify
            pipeline_key: Key of the trained pipeline to use
            preprocess: Whether to preprocess texts
            
        Returns:
            Array of prediction probabilities
        """
        if pipeline_key not in self.pipelines:
            raise ValueError(f"Pipeline {pipeline_key} not found. Train a model first.")
        
        if preprocess:
            processed_texts = self.preprocess_texts(texts)
        else:
            processed_texts = texts
        
        return self.pipelines[pipeline_key].predict_proba(processed_texts)
    
    def evaluate_model(self, texts: List[str], labels: List[str], 
                      pipeline_key: str, preprocess: bool = True) -> Dict[str, Any]:
        """
        Evaluate a trained model on new data.
        
        Args:
            texts: List of texts to evaluate
            labels: List of true labels
            pipeline_key: Key of the trained pipeline to use
            preprocess: Whether to preprocess texts
            
        Returns:
            Dictionary with evaluation metrics
        """
        predictions = self.predict(texts, pipeline_key, preprocess)
        metrics = self._calculate_metrics(labels, predictions)
        
        # Add classification report
        report = classification_report(labels, predictions, output_dict=True)
        
        return {
            'metrics': metrics,
            'classification_report': report,
            'confusion_matrix': confusion_matrix(labels, predictions).tolist()
        }
    
    def save_model(self, pipeline_key: str, filepath: str):
        """Save a trained model to disk."""
        if pipeline_key not in self.pipelines:
            raise ValueError(f"Pipeline {pipeline_key} not found.")
        
        model_data = {
            'pipeline': self.pipelines[pipeline_key],
            'preprocessor': self.preprocessor
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        
        print(f"Model saved to {filepath}")
    
    def load_model(self, filepath: str, pipeline_key: str):
        """Load a trained model from disk."""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)
        
        self.pipelines[pipeline_key] = model_data['pipeline']
        self.preprocessor = model_data['preprocessor']
        self.is_trained[pipeline_key] = True
        
        print(f"Model loaded from {filepath}")
    
    def compare_models(self, texts: List[str], labels: List[str], 
                      models: List[str] = None, 
                      vectorizers: List[str] = None,
                      test_size: float = 0.2) -> pd.DataFrame:
        """
        Compare multiple models and vectorizers.
        
        Args:
            texts: List of text samples
            labels: List of corresponding labels
            models: List of model names to compare
            vectorizers: List of vectorizer names to compare
            test_size: Proportion of data to use for testing
            
        Returns:
            DataFrame with comparison results
        """
        if models is None:
            models = list(self.available_models.keys())
        if vectorizers is None:
            vectorizers = list(self.available_vectorizers.keys())
        
        results = []
        
        for model_name in models:
            for vectorizer_name in vectorizers:
                print(f"\nTraining {model_name} with {vectorizer_name}...")
                result = self.train_model(
                    texts, labels, model_name, vectorizer_name, test_size
                )
                
                results.append({
                    'Model': model_name,
                    'Vectorizer': vectorizer_name,
                    'Accuracy': result['metrics']['accuracy'],
                    'Precision': result['metrics']['precision'],
                    'Recall': result['metrics']['recall'],
                    'F1-Score': result['metrics']['f1_score']
                })
        
        return pd.DataFrame(results)


def create_sample_data() -> Tuple[List[str], List[str]]:
    """Create sample data for demonstration."""
    texts = [
        "I love this product! It's amazing and works perfectly.",
        "This is the worst purchase I've ever made. Terrible quality.",
        "Great service and fast delivery. Highly recommended!",
        "Poor customer support. Very disappointed with the experience.",
        "Excellent quality and value for money. Will buy again.",
        "Not worth the price. Found better alternatives elsewhere.",
        "Outstanding performance and reliability. Five stars!",
        "Defective product arrived. Requesting immediate refund.",
        "Superb craftsmanship and attention to detail. Love it!",
        "Overpriced and underwhelming. Would not recommend.",
        "Perfect for my needs. Exactly what I was looking for.",
        "Broke after one week of use. Very poor build quality.",
        "Fantastic customer service and quick problem resolution.",
        "Misleading product description. Not as advertised.",
        "Best purchase this year! Exceeded all expectations."
    ]
    
    labels = [
        "positive", "negative", "positive", "negative", "positive",
        "negative", "positive", "negative", "positive", "negative",
        "positive", "negative", "positive", "negative", "positive"
    ]
    
    return texts, labels

"""
Example script demonstrating text classification training and evaluation.
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from text_classifier import TextClassifier, create_sample_data
from text_preprocessor import TextPreprocessor


def main():
    print("=== Text Classification Training Example ===\n")
    
    # Create sample data
    print("1. Loading sample data...")
    texts, labels = create_sample_data()
    print(f"Loaded {len(texts)} samples with {len(set(labels))} classes: {set(labels)}")
    
    # Initialize classifier
    print("\n2. Initializing text classifier...")
    classifier = TextClassifier()
    
    # Train Logistic Regression with TF-IDF
    print("\n3. Training Logistic Regression with TF-IDF...")
    lr_results = classifier.train_model(
        texts=texts,
        labels=labels,
        model_name='logistic_regression',
        vectorizer_name='tfidf',
        test_size=0.3,
        preprocess=True
    )
    
    print(f"Logistic Regression Results:")
    for metric, value in lr_results['metrics'].items():
        print(f"  {metric.capitalize()}: {value:.4f}")
    
    # Train Naive Bayes with Count Vectorizer
    print("\n4. Training Naive Bayes with Count Vectorizer...")
    nb_results = classifier.train_model(
        texts=texts,
        labels=labels,
        model_name='naive_bayes',
        vectorizer_name='count',
        test_size=0.3,
        preprocess=True
    )
    
    print(f"Naive Bayes Results:")
    for metric, value in nb_results['metrics'].items():
        print(f"  {metric.capitalize()}: {value:.4f}")
    
    # Compare models
    print("\n5. Comparing all model combinations...")
    comparison_df = classifier.compare_models(texts, labels, test_size=0.3)
    print(comparison_df.round(4))
    
    # Test predictions on new data
    print("\n6. Testing predictions on new data...")
    test_texts = [
        "This product is absolutely fantastic! I love it!",
        "Terrible quality and poor service. Very disappointed.",
        "Good value for money and works as expected.",
        "Worst experience ever. Would not recommend to anyone."
    ]
    
    # Use the best performing model (you can change this based on results)
    best_pipeline = 'logistic_regression_tfidf'
    
    predictions = classifier.predict(test_texts, best_pipeline)
    probabilities = classifier.predict_proba(test_texts, best_pipeline)
    
    print(f"Predictions using {best_pipeline}:")
    for i, (text, pred, prob) in enumerate(zip(test_texts, predictions, probabilities)):
        print(f"  Text {i+1}: '{text[:50]}...'")
        print(f"  Prediction: {pred}")
        print(f"  Confidence: {max(prob):.4f}")
        print()
    
    # Save the best model
    print("7. Saving the best model...")
    classifier.save_model(best_pipeline, 'best_text_classifier.pkl')
    
    # Demonstrate model evaluation
    print("\n8. Evaluating model on original data...")
    evaluation = classifier.evaluate_model(texts, labels, best_pipeline)
    
    print("Detailed Classification Report:")
    report = evaluation['classification_report']
    for class_name, metrics in report.items():
        if isinstance(metrics, dict):
            print(f"  {class_name}:")
            for metric, value in metrics.items():
                if isinstance(value, (int, float)):
                    print(f"    {metric}: {value:.4f}")
    
    # Create visualization
    print("\n9. Creating performance visualization...")
    create_performance_plot(comparison_df)
    
    print("\n=== Training Complete! ===")
    print(f"Best model saved as 'best_text_classifier.pkl'")
    print(f"Performance plot saved as 'model_comparison.png'")


def create_performance_plot(comparison_df):
    """Create a performance comparison plot."""
    plt.figure(figsize=(12, 8))
    
    # Reshape data for plotting
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
    
    # Create subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Text Classification Model Comparison', fontsize=16)
    
    for i, metric in enumerate(metrics):
        ax = axes[i//2, i%2]
        
        # Create bar plot
        bars = ax.bar(range(len(comparison_df)), comparison_df[metric], 
                     color=['skyblue', 'lightcoral', 'lightgreen', 'gold'][:len(comparison_df)])
        
        # Customize plot
        ax.set_title(f'{metric} Comparison')
        ax.set_ylabel(metric)
        ax.set_xlabel('Model + Vectorizer')
        ax.set_xticks(range(len(comparison_df)))
        ax.set_xticklabels([f"{row['Model']}\n{row['Vectorizer']}" 
                           for _, row in comparison_df.iterrows()], rotation=45)
        ax.set_ylim(0, 1)
        
        # Add value labels on bars
        for bar, value in zip(bars, comparison_df[metric]):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                   f'{value:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()


def demonstrate_hyperparameter_tuning():
    """Demonstrate hyperparameter tuning."""
    print("\n=== Hyperparameter Tuning Example ===")
    
    # Create larger sample data for better tuning
    texts, labels = create_sample_data()
    
    # Duplicate data to have more samples for tuning
    texts = texts * 5
    labels = labels * 5
    
    classifier = TextClassifier()
    
    print("Training with hyperparameter tuning (this may take a while)...")
    results = classifier.train_model(
        texts=texts,
        labels=labels,
        model_name='logistic_regression',
        vectorizer_name='tfidf',
        test_size=0.2,
        hyperparameter_tuning=True
    )
    
    print(f"Tuned model accuracy: {results['metrics']['accuracy']:.4f}")


def demonstrate_custom_data():
    """Demonstrate loading custom data from CSV."""
    print("\n=== Custom Data Example ===")
    
    # Create a sample CSV file
    sample_data = {
        'text': [
            "I love this movie! Great acting and plot.",
            "Boring movie with terrible acting.",
            "Amazing cinematography and soundtrack.",
            "Waste of time. Very disappointing.",
            "Best film I've seen this year!",
            "Poor script and direction.",
        ],
        'label': ['positive', 'negative', 'positive', 'negative', 'positive', 'negative']
    }
    
    df = pd.DataFrame(sample_data)
    df.to_csv('sample_movie_reviews.csv', index=False)
    
    # Load and train
    classifier = TextClassifier()
    texts, labels = classifier.load_data('sample_movie_reviews.csv')
    
    print(f"Loaded {len(texts)} samples from CSV")
    
    results = classifier.train_model(texts, labels, test_size=0.33)
    print(f"Model trained with accuracy: {results['metrics']['accuracy']:.4f}")


if __name__ == "__main__":
    main()
    
    # Uncomment to run additional examples
    # demonstrate_hyperparameter_tuning()
    # demonstrate_custom_data()

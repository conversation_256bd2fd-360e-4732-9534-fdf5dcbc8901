"""
Text Preprocessing Module

This module provides functions for cleaning and preprocessing text data.
Includes lowercase conversion, punctuation removal, stopword removal, and more.
"""

import re
import string
from typing import List, Set, Optional


class TextPreprocessor:
    """A comprehensive text preprocessing class."""
    
    def __init__(self, custom_stopwords: Optional[Set[str]] = None):
        """
        Initialize the TextPreprocessor.
        
        Args:
            custom_stopwords: Optional set of custom stopwords to use instead of default
        """
        # Default English stopwords
        self.default_stopwords = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'the', 'this', 'but', 'they', 'have',
            'had', 'what', 'said', 'each', 'which', 'she', 'do', 'how', 'their',
            'if', 'up', 'out', 'many', 'then', 'them', 'these', 'so', 'some',
            'her', 'would', 'make', 'like', 'into', 'him', 'time', 'two', 'more',
            'go', 'no', 'way', 'could', 'my', 'than', 'first', 'been', 'call',
            'who', 'oil', 'sit', 'now', 'find', 'down', 'day', 'did', 'get',
            'come', 'made', 'may', 'part'
        }
        
        self.stopwords = custom_stopwords if custom_stopwords else self.default_stopwords
    
    def to_lowercase(self, text: str) -> str:
        """Convert text to lowercase."""
        return text.lower()
    
    def remove_punctuation(self, text: str) -> str:
        """Remove punctuation from text."""
        return text.translate(str.maketrans('', '', string.punctuation))
    
    def remove_numbers(self, text: str) -> str:
        """Remove numbers from text."""
        return re.sub(r'\d+', '', text)
    
    def remove_extra_whitespace(self, text: str) -> str:
        """Remove extra whitespace and normalize spacing."""
        return ' '.join(text.split())
    
    def remove_stopwords(self, text: str) -> str:
        """Remove stopwords from text."""
        words = text.split()
        filtered_words = [word for word in words if word.lower() not in self.stopwords]
        return ' '.join(filtered_words)
    
    def remove_special_characters(self, text: str, keep_spaces: bool = True) -> str:
        """
        Remove special characters from text.
        
        Args:
            text: Input text
            keep_spaces: Whether to keep spaces (default: True)
        """
        if keep_spaces:
            pattern = r'[^a-zA-Z0-9\s]'
        else:
            pattern = r'[^a-zA-Z0-9]'
        return re.sub(pattern, '', text)
    
    def remove_urls(self, text: str) -> str:
        """Remove URLs from text."""
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        return re.sub(url_pattern, '', text)
    
    def remove_emails(self, text: str) -> str:
        """Remove email addresses from text."""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return re.sub(email_pattern, '', text)
    
    def clean_text(self, text: str, 
                   lowercase: bool = True,
                   remove_punct: bool = True,
                   remove_nums: bool = False,
                   remove_stops: bool = True,
                   remove_extra_ws: bool = True,
                   remove_special: bool = False,
                   remove_urls: bool = True,
                   remove_emails: bool = True) -> str:
        """
        Comprehensive text cleaning function.
        
        Args:
            text: Input text to clean
            lowercase: Convert to lowercase
            remove_punct: Remove punctuation
            remove_nums: Remove numbers
            remove_stops: Remove stopwords
            remove_extra_ws: Remove extra whitespace
            remove_special: Remove special characters
            remove_urls: Remove URLs
            remove_emails: Remove email addresses
            
        Returns:
            Cleaned text
        """
        if not isinstance(text, str):
            return ""
        
        cleaned_text = text
        
        # Remove URLs and emails first
        if remove_urls:
            cleaned_text = self.remove_urls(cleaned_text)
        
        if remove_emails:
            cleaned_text = self.remove_emails(cleaned_text)
        
        # Convert to lowercase
        if lowercase:
            cleaned_text = self.to_lowercase(cleaned_text)
        
        # Remove punctuation
        if remove_punct:
            cleaned_text = self.remove_punctuation(cleaned_text)
        
        # Remove numbers
        if remove_nums:
            cleaned_text = self.remove_numbers(cleaned_text)
        
        # Remove special characters
        if remove_special:
            cleaned_text = self.remove_special_characters(cleaned_text)
        
        # Remove extra whitespace
        if remove_extra_ws:
            cleaned_text = self.remove_extra_whitespace(cleaned_text)
        
        # Remove stopwords (do this after other cleaning to ensure proper word separation)
        if remove_stops:
            cleaned_text = self.remove_stopwords(cleaned_text)
        
        return cleaned_text
    
    def tokenize(self, text: str) -> List[str]:
        """Simple tokenization by splitting on whitespace."""
        return text.split()
    
    def preprocess_batch(self, texts: List[str], **kwargs) -> List[str]:
        """
        Preprocess a batch of texts.
        
        Args:
            texts: List of texts to preprocess
            **kwargs: Arguments to pass to clean_text method
            
        Returns:
            List of preprocessed texts
        """
        return [self.clean_text(text, **kwargs) for text in texts]


# Convenience functions for quick use
def preprocess_text(text: str, **kwargs) -> str:
    """
    Quick text preprocessing function.
    
    Args:
        text: Input text
        **kwargs: Arguments to pass to TextPreprocessor.clean_text
        
    Returns:
        Preprocessed text
    """
    preprocessor = TextPreprocessor()
    return preprocessor.clean_text(text, **kwargs)


def preprocess_texts(texts: List[str], **kwargs) -> List[str]:
    """
    Quick batch text preprocessing function.
    
    Args:
        texts: List of input texts
        **kwargs: Arguments to pass to TextPreprocessor.clean_text
        
    Returns:
        List of preprocessed texts
    """
    preprocessor = TextPreprocessor()
    return preprocessor.preprocess_batch(texts, **kwargs)


if __name__ == "__main__":
    # Example usage
    sample_text = """
    Hello World! This is a SAMPLE text with punctuation, numbers123, 
    and extra    spaces. Visit https://example.com <NAME_EMAIL>.
    This text contains stopwords like 'the', 'and', 'is'.
    """
    
    print("Original text:")
    print(sample_text)
    print("\n" + "="*50 + "\n")
    
    # Create preprocessor
    preprocessor = TextPreprocessor()
    
    # Clean the text
    cleaned = preprocessor.clean_text(sample_text)
    print("Cleaned text:")
    print(cleaned)
    
    # Show individual steps
    print("\n" + "="*50 + "\n")
    print("Step-by-step preprocessing:")
    
    step1 = preprocessor.to_lowercase(sample_text)
    print(f"1. Lowercase: {step1}")
    
    step2 = preprocessor.remove_punctuation(step1)
    print(f"2. Remove punctuation: {step2}")
    
    step3 = preprocessor.remove_extra_whitespace(step2)
    print(f"3. Remove extra whitespace: {step3}")
    
    step4 = preprocessor.remove_stopwords(step3)
    print(f"4. Remove stopwords: {step4}")

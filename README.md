# Text Classification with Logistic Regression and Naive Bayes

This project provides a comprehensive text classification system using machine learning algorithms including Logistic Regression and Naive Bayes. It includes text preprocessing, feature extraction, model training, evaluation, and prediction capabilities.

## Features

### 🔧 Text Preprocessing
- **Lowercase conversion** - Normalize text case
- **Punctuation removal** - Clean special characters
- **Stopword removal** - Remove common English words
- **URL/Email removal** - Clean web addresses and emails
- **Whitespace normalization** - Remove extra spaces
- **Custom preprocessing options** - Configurable cleaning steps

### 🤖 Machine Learning Models
- **Logistic Regression** - Linear classification with regularization
- **Naive Bayes** - Probabilistic classification
- **Multiple vectorizers** - TF-IDF and Count vectorization
- **Hyperparameter tuning** - Grid search optimization
- **Cross-validation** - Robust model evaluation

### 📊 Evaluation & Analysis
- **Performance metrics** - Accuracy, Precision, Recall, F1-score
- **Classification reports** - Detailed per-class analysis
- **Confusion matrices** - Error analysis
- **Prediction probabilities** - Confidence scores
- **Model comparison** - Side-by-side performance analysis

## Files Structure

```
├── text_preprocessor.py      # Text cleaning and preprocessing
├── text_classifier.py        # Main classification module
├── simple_classifier_demo.py # Basic usage example
├── train_classifier_example.py # Advanced training example
├── test_models.py            # Comprehensive testing
├── requirements.txt          # Dependencies
└── README.md                # This file
```

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. The main dependencies are:
- scikit-learn >= 1.0.0
- pandas >= 1.3.0
- numpy >= 1.21.0
- matplotlib >= 3.5.0
- seaborn >= 0.11.0

## Quick Start

### Basic Usage

```python
from text_classifier import TextClassifier, create_sample_data

# Load sample data
texts, labels = create_sample_data()

# Initialize classifier
classifier = TextClassifier()

# Train a model
results = classifier.train_model(
    texts=texts,
    labels=labels,
    model_name='logistic_regression',
    vectorizer_name='tfidf',
    test_size=0.2
)

# Make predictions
new_texts = ["Great product!", "Terrible experience."]
predictions = classifier.predict(new_texts, 'logistic_regression_tfidf')
print(predictions)  # ['positive', 'negative']
```

### Advanced Usage

```python
# Compare multiple models
comparison_df = classifier.compare_models(texts, labels)
print(comparison_df)

# Train with hyperparameter tuning
results = classifier.train_model(
    texts, labels,
    hyperparameter_tuning=True
)

# Get prediction probabilities
probabilities = classifier.predict_proba(new_texts, 'logistic_regression_tfidf')

# Save and load models
classifier.save_model('logistic_regression_tfidf', 'my_model.pkl')
classifier.load_model('my_model.pkl', 'logistic_regression_tfidf')
```

## Running Examples

### 1. Simple Demo
```bash
python simple_classifier_demo.py
```
Basic classification with sample data.

### 2. Comprehensive Testing
```bash
python test_models.py
```
Tests all features including model comparison, evaluation, and persistence.

### 3. Advanced Training (with plotting)
```bash
python train_classifier_example.py
```
Full example with visualization and detailed analysis.

## Model Performance

The system supports multiple model configurations:

| Model | Vectorizer | Typical Use Case |
|-------|------------|------------------|
| Logistic Regression + TF-IDF | Best for balanced datasets |
| Logistic Regression + Count | Good for short texts |
| Naive Bayes + TF-IDF | Fast training, good baseline |
| Naive Bayes + Count | Traditional text classification |

## Text Preprocessing Options

```python
from text_preprocessor import TextPreprocessor

preprocessor = TextPreprocessor()

# Full preprocessing
cleaned = preprocessor.clean_text(
    text,
    lowercase=True,
    remove_punct=True,
    remove_stops=True,
    remove_urls=True,
    remove_emails=True
)

# Custom preprocessing
cleaned = preprocessor.clean_text(
    text,
    lowercase=True,
    remove_punct=False,  # Keep punctuation
    remove_stops=False,  # Keep stopwords
    remove_nums=True     # Remove numbers
)
```

## Sample Output

```
=== Simple Text Classification Demo ===

1. Loading sample data...
Loaded 15 samples with 2 classes: {'positive', 'negative'}

2. Initializing text classifier...

3. Training Logistic Regression with TF-IDF...
Preprocessing texts...
Training logistic_regression with tfidf vectorizer...
Training completed! Accuracy: 0.6000

Logistic Regression Results:
  Accuracy: 0.6000
  Precision: 0.6000
  Recall: 0.6000
  F1_score: 0.6000

4. Training Naive Bayes with Count Vectorizer...
Training completed! Accuracy: 0.6000

5. Testing predictions on new data...
Predictions using logistic_regression_tfidf:
  Text 1: 'This product is absolutely fantastic! I love it!'
  Prediction: positive
  Confidence: 0.5604

  Text 2: 'Terrible quality and poor service. Very disappointed.'
  Prediction: negative
  Confidence: 0.5668
```

## Customization

### Custom Stopwords
```python
custom_stopwords = {'the', 'and', 'or', 'but'}
preprocessor = TextPreprocessor(custom_stopwords=custom_stopwords)
classifier = TextClassifier(preprocessor=preprocessor)
```

### Custom Data
```python
# From CSV file
texts, labels = classifier.load_data('my_data.csv')

# From lists
texts = ["Text 1", "Text 2", ...]
labels = ["positive", "negative", ...]
```

## Tips for Better Performance

1. **More Data**: Increase training data size for better accuracy
2. **Balanced Classes**: Ensure roughly equal positive/negative samples
3. **Preprocessing**: Experiment with different preprocessing options
4. **Hyperparameter Tuning**: Use `hyperparameter_tuning=True` for optimization
5. **Feature Engineering**: Try different vectorizer parameters

## Troubleshooting

- **Low Accuracy**: Try more training data or different preprocessing
- **Memory Issues**: Reduce `max_features` in vectorizers
- **Slow Training**: Disable hyperparameter tuning for faster results

## Contributing

Feel free to extend the system with:
- Additional algorithms (SVM, Random Forest, etc.)
- More preprocessing options
- Advanced feature extraction methods
- Different evaluation metrics

## License

This project is open source and available under the MIT License.
